/**
 * @description 考试配置文件
 * <AUTHOR>
 * @date 2025-04-16
 */

// 考试配置
export const examConfig = {
	// 基础配置
	basic: {
		defaultDuration: 15, // 默认考试时长（分钟）
		questionsPerExam: 10, // 每次考试题目数量
		passingScore: 60, // 及格分数
		maxAttempts: 3, // 最大考试次数
		autoSubmit: true, // 时间到自动提交
		allowReview: true, // 允许查看答案解析
	},
	
	// 难度配置
	difficulty: {
		easy: {
			name: '简单',
			percentage: 60, // 简单题目占比
			timePerQuestion: 60 // 每题建议时间（秒）
		},
		medium: {
			name: '中等',
			percentage: 30,
			timePerQuestion: 90
		},
		hard: {
			name: '困难',
			percentage: 10,
			timePerQuestion: 120
		}
	},
	
	// 分类配置
	categories: {
		'基础知识': {
			name: '安全生产基础知识',
			color: '#1E90FF',
			icon: '📚'
		},
		'法律法规': {
			name: '安全生产法律法规',
			color: '#FF6B6B',
			icon: '⚖️'
		},
		'操作规程': {
			name: '安全操作规程',
			color: '#4ECDC4',
			icon: '🔧'
		},
		'应急处理': {
			name: '应急处理措施',
			color: '#45B7D1',
			icon: '🚨'
		},
		'防护用品': {
			name: '个人防护用品',
			color: '#96CEB4',
			icon: '🦺'
		}
	},
	
	// 评分标准
	scoring: {
		excellent: { min: 90, label: '优秀', color: '#52C41A', icon: '🏆' },
		good: { min: 80, label: '良好', color: '#1890FF', icon: '👍' },
		average: { min: 70, label: '中等', color: '#FAAD14', icon: '👌' },
		pass: { min: 60, label: '及格', color: '#FA8C16', icon: '✅' },
		fail: { min: 0, label: '不及格', color: '#F5222D', icon: '❌' }
	}
}

// 获取评分等级
export function getScoreLevel(score) {
	const { scoring } = examConfig
	if (score >= scoring.excellent.min) return scoring.excellent
	if (score >= scoring.good.min) return scoring.good
	if (score >= scoring.average.min) return scoring.average
	if (score >= scoring.pass.min) return scoring.pass
	return scoring.fail
}

// 根据难度分布生成题目
export function generateExamByDifficulty(questionBank, totalQuestions = 10) {
	const { difficulty } = examConfig
	const easyCount = Math.floor(totalQuestions * difficulty.easy.percentage / 100)
	const mediumCount = Math.floor(totalQuestions * difficulty.medium.percentage / 100)
	const hardCount = totalQuestions - easyCount - mediumCount
	
	// 按难度分类题目
	const easyQuestions = questionBank.filter(q => q.difficulty === 'easy')
	const mediumQuestions = questionBank.filter(q => q.difficulty === 'medium')
	const hardQuestions = questionBank.filter(q => q.difficulty === 'hard')
	
	// 随机选择题目
	const selectedQuestions = [
		...getRandomItems(easyQuestions, easyCount),
		...getRandomItems(mediumQuestions, mediumCount),
		...getRandomItems(hardQuestions, hardCount)
	]
	
	// 打乱题目顺序
	return selectedQuestions.sort(() => Math.random() - 0.5)
}

// 随机选择数组中的指定数量元素
function getRandomItems(array, count) {
	const shuffled = [...array].sort(() => Math.random() - 0.5)
	return shuffled.slice(0, Math.min(count, array.length))
}

// 计算考试统计信息
export function calculateExamStats(examRecord, questionBank) {
	const stats = {
		totalQuestions: examRecord.questions?.length || 0,
		correctAnswers: 0,
		wrongAnswers: 0,
		accuracy: 0,
		categoryStats: {},
		difficultyStats: {}
	}
	
	if (!examRecord.questions) return stats
	
	examRecord.questions.forEach(question => {
		const isCorrect = question.userAnswer === question.answer
		if (isCorrect) {
			stats.correctAnswers++
		} else {
			stats.wrongAnswers++
		}
		
		// 分类统计
		const category = question.category || '未分类'
		if (!stats.categoryStats[category]) {
			stats.categoryStats[category] = { total: 0, correct: 0 }
		}
		stats.categoryStats[category].total++
		if (isCorrect) stats.categoryStats[category].correct++
		
		// 难度统计
		const difficulty = question.difficulty || 'medium'
		if (!stats.difficultyStats[difficulty]) {
			stats.difficultyStats[difficulty] = { total: 0, correct: 0 }
		}
		stats.difficultyStats[difficulty].total++
		if (isCorrect) stats.difficultyStats[difficulty].correct++
	})
	
	stats.accuracy = stats.totalQuestions > 0 ? 
		Math.round((stats.correctAnswers / stats.totalQuestions) * 100) : 0
	
	return stats
}
