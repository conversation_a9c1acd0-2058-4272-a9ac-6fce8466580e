/**
 * @description 管理后台页面
 * <AUTHOR>
 * @date 2025-04-16
 */

<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="nav-bar">
			<text class="title">管理后台</text>
			<text class="logout" @click="logout">退出</text>
		</view>
		
		<!-- 导出按钮 -->
		<view class="export-section">
			<view class="export-options">
				<view class="export-btn" @click="exportData('csv')" :class="{ 'loading': isExporting }">
					<text>导出CSV</text>
				</view>
				<view class="export-btn" @click="exportData('excel')" :class="{ 'loading': isExporting }">
					<text>导出Excel</text>
				</view>
				<view class="export-btn" @click="exportData('report')" :class="{ 'loading': isExporting }">
					<text>分析报告</text>
				</view>
			</view>
			<view class="export-options" style="margin-top: 20rpx;">
				<view class="export-btn backup-btn" @click="createBackup">
					<text>创建备份</text>
				</view>
				<view class="export-btn backup-btn" @click="manageBackups">
					<text>备份管理</text>
				</view>
				<view class="export-btn system-btn" @click="viewSystemStatus">
					<text>系统状态</text>
				</view>
			</view>
			<view class="export-options" style="margin-top: 20rpx;">
				<view class="export-btn question-bank-btn" @click="navigateToQuestionBank">
					<text>题库管理</text>
				</view>
			</view>
			<view class="export-tip">
				<text>导出后的数据可粘贴到Excel中使用，备份功能可保护重要数据</text>
			</view>
		</view>
		
		<!-- 考试记录表格 - 重新设计为静态布局 -->
		<view class="table-container">
			<!-- 表头行 -->
			<view class="table-header">
				<view class="cell-name">姓名</view>
				<view class="cell-dept">部门</view>
				<view class="cell-score">分数</view>
				<view class="cell-rank">排名</view>
				<view class="cell-time">考试时间</view>
			</view>
			
			<!-- 数据行 -->
			<view class="table-content">
				<view class="table-row" v-for="(record, index) in recordsWithRank" :key="index">
					<view class="cell-name">{{ record.name }}</view>
					<view class="cell-dept">{{ record.department }}</view>
					<view class="cell-score">{{ record.score }}</view>
					<view class="cell-rank">{{ record.rank }}</view>
					<view class="cell-time">{{ formatTime(record.submitTime) }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { analyzeExamData, generateExamReport, exportToCSV, exportToExcel } from '@/utils/dataAnalysis.js'
	import { logger, errorHandler } from '@/utils/errorHandler.js'
	import { messageManager, loadingManager } from '@/utils/userExperience.js'
	import { dataBackupManager } from '@/utils/dataBackup.js'

	// 定义考试记录类型
	interface ExamRecord {
		examId: number;
		title: string;
		name: string;
		department: string;
		score: number;
		submitTime: number;
		rank?: number;
	}
	
	export default {
		data() {
			return {
				examRecords: [] as ExamRecord[],
				recordsWithRank: [] as ExamRecord[],
				analysisData: null,
				isExporting: false
			}
		},
		onShow() {
			// 检查登录状态
			const token = uni.getStorageSync('adminToken')
			if (!token) {
				uni.redirectTo({
					url: '/pages/admin/login'
				})
				return
			}
			
			// 加载考试记录
			this.loadExamRecords()
		},
		methods: {
			// 加载考试记录
			loadExamRecords() {
				try {
					const records = uni.getStorageSync('examRecords') || []
					logger.info('Loaded exam records', { count: records.length })
				
				// 对记录进行分组处理，同名同部门只保留最新一条记录
				const recordMap = new Map()
				
				// 首先按时间从新到旧排序
				const sortedByTime = [...records].sort((a, b) => b.submitTime - a.submitTime)
				
				// 遍历记录，只保留同名同部门的最新一条
				sortedByTime.forEach(record => {
					const key = `${record.name}-${record.department}`
					if (!recordMap.has(key)) {
						recordMap.set(key, record)
					}
				})
				
				// 转换回数组
				const uniqueRecords = Array.from(recordMap.values())
				
				// 按分数排序以计算排名
				const sortedByScore = [...uniqueRecords].sort((a, b) => b.score - a.score)
				
				// 添加排名
				const recordsWithRank = sortedByScore.map((record, index) => {
					return {
						...record,
						rank: index + 1
					}
				})
				
				// 按时间排序（最新的在前）
				this.recordsWithRank = recordsWithRank.sort((a, b) => b.submitTime - a.submitTime) as ExamRecord[]
				this.examRecords = uniqueRecords as ExamRecord[]

				// 生成分析数据
				this.analysisData = analyzeExamData(this.examRecords)
				logger.info('Analysis data generated', this.analysisData)
			} catch (error) {
				logger.error('Failed to load exam records', error)
				messageManager.error('加载考试记录失败')
			}
		},
			
			// 格式化时间
			formatTime(timestamp: number): string {
				const date = new Date(timestamp)
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
			},
			
			// 导出数据
			async exportData(type = 'csv') {
				if (this.examRecords.length === 0) {
					messageManager.warning('没有可导出的数据')
					return
				}

				this.isExporting = true
				loadingManager.show('export', { title: '正在导出数据...' })

				try {
					if (type === 'csv') {
						await this.exportCSVEnhanced()
					} else if (type === 'excel') {
						await this.exportExcelEnhanced()
					} else if (type === 'report') {
						await this.exportAnalysisReport()
					}
				} catch (error) {
					logger.error('Export failed', error)
					messageManager.error('导出失败，请重试')
				} finally {
					this.isExporting = false
					loadingManager.hide('export')
				}
			},
			
			// 增强的CSV导出
			async exportCSVEnhanced() {
				const csvContent = exportToCSV(this.recordsWithRank)

				await uni.setClipboardData({
					data: csvContent
				})

				messageManager.success('CSV数据已复制到剪贴板')
				logger.info('CSV export completed', { recordCount: this.recordsWithRank.length })
			},

			// 增强的Excel导出
			async exportExcelEnhanced() {
				const excelContent = exportToExcel(this.recordsWithRank)

				await uni.setClipboardData({
					data: excelContent
				})

				messageManager.success('Excel数据已复制到剪贴板')
				logger.info('Excel export completed', { recordCount: this.recordsWithRank.length })
			},

			// 导出分析报告
			async exportAnalysisReport() {
				const report = generateExamReport(this.examRecords)

				await uni.setClipboardData({
					data: report
				})

				messageManager.success('分析报告已复制到剪贴板')
				logger.info('Analysis report exported')
			},
			
			// 导出Excel（通过HTML表格格式，更适合Excel粘贴）
			exportExcel() {
				// 构建HTML表格内容
				let htmlContent = '<table border="1">\n'
				// 添加表头
				htmlContent += '<tr><th>姓名</th><th>部门</th><th>分数</th><th>排名</th><th>考试时间</th></tr>\n'
				
				// 添加数据行
				this.recordsWithRank.forEach((record) => {
					htmlContent += '<tr>'
					htmlContent += `<td>${record.name}</td>`
					htmlContent += `<td>${record.department}</td>`
					htmlContent += `<td>${record.score}</td>`
					htmlContent += `<td>${record.rank}</td>`
					htmlContent += `<td>${this.formatTime(record.submitTime)}</td>`
					htmlContent += '</tr>\n'
				})
				htmlContent += '</table>'
				
				// 保存HTML表格到剪贴板
				uni.showModal({
					title: '导出Excel格式成功',
					content: '数据已复制到剪贴板，粘贴到Excel中即可保持表格格式',
					showCancel: false,
					success: () => {
						uni.setClipboardData({
							data: htmlContent,
							success: () => {
								uni.showToast({
									title: '表格数据已复制',
									icon: 'success'
								})
							}
						})
					}
				})
			},
			
			// 创建备份
			async createBackup() {
				loadingManager.show('backup', { title: '正在创建备份...' })

				try {
					const result = await dataBackupManager.createFullBackup()
					if (result.success) {
						messageManager.success('备份创建成功')
						logger.info('Backup created successfully', result)
					} else {
						messageManager.error('备份创建失败')
					}
				} catch (error) {
					logger.error('Backup creation failed', error)
					messageManager.error('备份创建失败')
				} finally {
					loadingManager.hide('backup')
				}
			},

			// 备份管理
			manageBackups() {
				uni.navigateTo({
					url: '/pages/admin/backupManager'
				})
			},

			// 查看系统状态
			viewSystemStatus() {
				uni.navigateTo({
					url: '/pages/admin/systemStatus'
				})
			},

			// 跳转到题库管理页面
			navigateToQuestionBank() {
				uni.navigateTo({
					url: '/pages/admin/questionBank'
				})
			},
			
			// 退出登录
			logout() {
				uni.removeStorageSync('adminToken')
				uni.redirectTo({
					url: '/pages/admin/login'
				})
			}
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	/* 导航栏样式 */
	.nav-bar {
		background-color: #1E90FF;
		padding: 20rpx 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.title {
		font-size: 32rpx;
		color: #fff;
		font-weight: bold;
	}

	.logout {
		font-size: 28rpx;
		color: #fff;
	}
	
	/* 导出按钮样式 */
	.export-section {
		padding: 20rpx 40rpx;
	}
	
	.export-options {
		display: flex;
		justify-content: space-between;
	}
	
	.export-btn {
		background-color: #1E90FF;
		border-radius: 10rpx;
		padding: 16rpx 0;
		text-align: center;
		flex: 1;
		margin: 0 10rpx;
		transition: all 0.3s;
	}

	.export-btn.loading {
		background-color: #89c0fa;
		opacity: 0.8;
	}

	.export-btn.backup-btn {
		background-color: #52C41A;
	}

	.export-btn.system-btn {
		background-color: #722ED1;
	}

	.export-btn text {
		font-size: 28rpx;
		color: #fff;
	}
	
	.export-tip {
		margin-top: 10rpx;
		text-align: center;
	}
	
	.export-tip text {
		font-size: 24rpx;
		color: #666;
	}

	/* 新表格样式 */
	.table-container {
		margin: 20rpx;
		background-color: #fff;
		border: 1px solid #000;
	}
	
	/* 表头样式 */
	.table-header {
		display: grid;
		grid-template-columns: 150rpx 200rpx 100rpx 100rpx 180rpx;
		background-color: #f0f0f0;
		font-weight: bold;
		font-size: 28rpx;
		color: #333;
		border-bottom: 1px solid #000;
	}
	
	/* 表格内容样式 */
	.table-content {
		font-size: 28rpx;
		color: #333;
	}
	
	/* 表格行样式 */
	.table-row {
		display: grid;
		grid-template-columns: 150rpx 200rpx 100rpx 100rpx 180rpx;
		border-bottom: 1px solid #000;
	}
	
	/* 单元格共有样式 */
	.table-header > view, .table-row > view {
		padding: 16rpx;
		text-align: center;
		border-right: 1px solid #000;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	
	.table-header > view:last-child, .table-row > view:last-child {
		border-right: none;
	}
	
	/* 特定单元格样式 */
	.cell-name {
		
	}
	
	.cell-dept {
		
	}
	
	.cell-score {
		
	}
	
	.cell-rank {
		
	}
	
	.cell-time {
		
	}
</style> 