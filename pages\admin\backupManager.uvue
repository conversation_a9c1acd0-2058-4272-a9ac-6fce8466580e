/**
 * @description 备份管理页面
 * <AUTHOR>
 * @date 2025-04-16
 */

<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="nav-bar">
			<text class="back-btn" @click="goBack">← 返回</text>
			<text class="title">备份管理</text>
			<text class="create-btn" @click="createBackup">创建备份</text>
		</view>
		
		<!-- 存储信息 -->
		<view class="storage-info" v-if="storageInfo">
			<view class="info-card">
				<view class="info-item">
					<text class="info-label">备份数量</text>
					<text class="info-value">{{ storageInfo.backupCount }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">备份大小</text>
					<text class="info-value">{{ formatSize(storageInfo.backupSize) }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">可用空间</text>
					<text class="info-value">{{ formatSize(storageInfo.availableSpace) }}</text>
				</view>
			</view>
		</view>
		
		<!-- 备份列表 -->
		<view class="backup-list">
			<view class="section-title">
				<text>备份记录</text>
			</view>
			
			<view v-if="backupList.length === 0" class="empty-state">
				<text>暂无备份记录</text>
				<view class="create-backup-btn" @click="createBackup">
					<text>创建第一个备份</text>
				</view>
			</view>
			
			<view v-else>
				<view class="backup-item" v-for="backup in backupList" :key="backup.id">
					<view class="backup-info">
						<view class="backup-date">{{ backup.date }}</view>
						<view class="backup-details">
							<text class="backup-size">{{ formatSize(backup.size) }}</text>
							<text class="backup-version">v{{ backup.version }}</text>
						</view>
					</view>
					<view class="backup-actions">
						<view class="action-btn restore-btn" @click="restoreBackup(backup.id)">
							<text>恢复</text>
						</view>
						<view class="action-btn export-btn" @click="exportBackup(backup.id)">
							<text>导出</text>
						</view>
						<view class="action-btn delete-btn" @click="deleteBackup(backup.id)">
							<text>删除</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 导入备份 -->
		<view class="import-section">
			<view class="section-title">
				<text>导入备份</text>
			</view>
			<view class="import-card">
				<text class="import-desc">可以导入之前导出的备份文件</text>
				<view class="import-btn" @click="showImportDialog">
					<text>导入备份数据</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { dataBackupManager } from '@/utils/dataBackup.js'
	import { logger, errorHandler } from '@/utils/errorHandler.js'
	import { messageManager, loadingManager, showConfirm } from '@/utils/userExperience.js'
	
	export default {
		data() {
			return {
				backupList: [],
				storageInfo: null,
				isLoading: false
			}
		},
		onLoad() {
			this.loadBackupData()
		},
		onShow() {
			this.loadBackupData()
		},
		methods: {
			// 加载备份数据
			loadBackupData() {
				try {
					this.backupList = dataBackupManager.getBackupList()
					this.storageInfo = dataBackupManager.getStorageInfo()
					logger.info('Backup data loaded', { 
						backupCount: this.backupList.length,
						storageInfo: this.storageInfo 
					})
				} catch (error) {
					logger.error('Failed to load backup data', error)
					messageManager.error('加载备份数据失败')
				}
			},
			
			// 创建备份
			async createBackup() {
				loadingManager.show('createBackup', { title: '正在创建备份...' })
				
				try {
					const result = await dataBackupManager.createFullBackup()
					if (result.success) {
						messageManager.success('备份创建成功')
						this.loadBackupData() // 刷新列表
						logger.info('Backup created successfully', result)
					} else {
						messageManager.error('备份创建失败')
					}
				} catch (error) {
					logger.error('Backup creation failed', error)
					messageManager.error('备份创建失败')
				} finally {
					loadingManager.hide('createBackup')
				}
			},
			
			// 恢复备份
			async restoreBackup(backupId) {
				const confirmed = await showConfirm({
					title: '确认恢复',
					content: '恢复备份将覆盖当前数据，此操作不可撤销。确定要继续吗？',
					confirmText: '确定恢复',
					cancelText: '取消'
				})
				
				if (!confirmed) return
				
				loadingManager.show('restore', { title: '正在恢复备份...' })
				
				try {
					const result = await dataBackupManager.restoreBackup(backupId)
					if (result.success) {
						messageManager.success('备份恢复成功')
						logger.info('Backup restored successfully', result)
						
						// 提示重启应用
						setTimeout(() => {
							uni.showModal({
								title: '恢复完成',
								content: '备份已成功恢复，建议重启应用以确保数据正确加载',
								showCancel: false,
								confirmText: '知道了'
							})
						}, 1000)
					} else {
						messageManager.error('备份恢复失败')
					}
				} catch (error) {
					logger.error('Backup restore failed', error)
					messageManager.error('备份恢复失败')
				} finally {
					loadingManager.hide('restore')
				}
			},
			
			// 导出备份
			async exportBackup(backupId) {
				try {
					const result = dataBackupManager.exportBackup(backupId)
					if (result.success) {
						await uni.setClipboardData({
							data: result.data
						})
						messageManager.success('备份数据已复制到剪贴板')
						logger.info('Backup exported successfully', { backupId })
					} else {
						messageManager.error('导出失败')
					}
				} catch (error) {
					logger.error('Export backup failed', error)
					messageManager.error('导出失败')
				}
			},
			
			// 删除备份
			async deleteBackup(backupId) {
				const confirmed = await showConfirm({
					title: '确认删除',
					content: '确定要删除这个备份吗？删除后无法恢复。',
					confirmText: '确定删除',
					cancelText: '取消'
				})
				
				if (!confirmed) return
				
				try {
					const result = dataBackupManager.deleteBackup(backupId)
					if (result.success) {
						messageManager.success('备份已删除')
						this.loadBackupData() // 刷新列表
						logger.info('Backup deleted successfully', { backupId })
					} else {
						messageManager.error('删除失败')
					}
				} catch (error) {
					logger.error('Delete backup failed', error)
					messageManager.error('删除失败')
				}
			},
			
			// 显示导入对话框
			showImportDialog() {
				uni.showModal({
					title: '导入备份',
					content: '请将备份数据粘贴到剪贴板，然后点击确定导入',
					success: async (res) => {
						if (res.confirm) {
							await this.importBackup()
						}
					}
				})
			},
			
			// 导入备份
			async importBackup() {
				try {
					const clipboardData = await uni.getClipboardData()
					const backupData = clipboardData.data
					
					if (!backupData) {
						messageManager.warning('剪贴板中没有数据')
						return
					}
					
					loadingManager.show('import', { title: '正在导入备份...' })
					
					const result = await dataBackupManager.importBackup(backupData)
					if (result.success) {
						messageManager.success('备份导入成功')
						this.loadBackupData() // 刷新列表
						logger.info('Backup imported successfully', result)
					} else {
						messageManager.error('备份导入失败：' + result.error)
					}
				} catch (error) {
					logger.error('Import backup failed', error)
					messageManager.error('备份导入失败')
				} finally {
					loadingManager.hide('import')
				}
			},
			
			// 格式化文件大小
			formatSize(bytes) {
				if (bytes === 0) return '0 B'
				const k = 1024
				const sizes = ['B', 'KB', 'MB', 'GB']
				const i = Math.floor(Math.log(bytes) / Math.log(k))
				return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
			},
			
			// 返回
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 导航栏 */
	.nav-bar {
		background-color: #1E90FF;
		padding: 20rpx 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.back-btn, .create-btn {
		font-size: 28rpx;
		color: #fff;
	}
	
	.title {
		font-size: 32rpx;
		color: #fff;
		font-weight: bold;
	}
	
	/* 存储信息 */
	.storage-info {
		padding: 30rpx;
	}
	
	.info-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-around;
	}
	
	.info-item {
		text-align: center;
	}
	
	.info-label {
		font-size: 26rpx;
		color: #666;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.info-value {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}
	
	/* 备份列表 */
	.backup-list {
		padding: 0 30rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.empty-state {
		text-align: center;
		padding: 60rpx 0;
		color: #999;
	}
	
	.create-backup-btn {
		background-color: #1E90FF;
		color: #fff;
		padding: 20rpx 40rpx;
		border-radius: 40rpx;
		margin-top: 30rpx;
		display: inline-block;
	}
	
	.backup-item {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.backup-date {
		font-size: 30rpx;
		color: #333;
		font-weight: bold;
	}
	
	.backup-details {
		margin-top: 10rpx;
	}
	
	.backup-size, .backup-version {
		font-size: 24rpx;
		color: #666;
		margin-right: 20rpx;
	}
	
	.backup-actions {
		display: flex;
		gap: 10rpx;
	}
	
	.action-btn {
		padding: 12rpx 20rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
	}
	
	.restore-btn {
		background-color: #52C41A;
		color: #fff;
	}
	
	.export-btn {
		background-color: #1890FF;
		color: #fff;
	}
	
	.delete-btn {
		background-color: #FF4D4F;
		color: #fff;
	}
	
	/* 导入区域 */
	.import-section {
		padding: 30rpx;
	}
	
	.import-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		text-align: center;
	}
	
	.import-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 30rpx;
		display: block;
	}
	
	.import-btn {
		background-color: #52C41A;
		color: #fff;
		padding: 20rpx 40rpx;
		border-radius: 40rpx;
		display: inline-block;
	}
</style>
