/**
 * @description 错误处理和日志系统
 * <AUTHOR>
 * @date 2025-04-16
 */

// 日志级别
const LOG_LEVELS = {
	DEBUG: 0,
	INFO: 1,
	WARN: 2,
	ERROR: 3
}

// 日志管理器
class Logger {
	constructor() {
		this.level = LOG_LEVELS.INFO
		this.logs = []
		this.maxLogs = 1000
	}
	
	setLevel(level) {
		this.level = level
	}
	
	log(level, message, data = null) {
		if (level < this.level) return
		
		const logEntry = {
			timestamp: new Date().toISOString(),
			level: Object.keys(LOG_LEVELS)[level],
			message,
			data,
			stack: new Error().stack
		}
		
		this.logs.push(logEntry)
		
		// 限制日志数量
		if (this.logs.length > this.maxLogs) {
			this.logs.shift()
		}
		
		// 控制台输出
		this.consoleOutput(logEntry)
		
		// 持久化存储
		this.persistLog(logEntry)
	}
	
	debug(message, data) {
		this.log(LOG_LEVELS.DEBUG, message, data)
	}
	
	info(message, data) {
		this.log(LOG_LEVELS.INFO, message, data)
	}
	
	warn(message, data) {
		this.log(LOG_LEVELS.WARN, message, data)
	}
	
	error(message, data) {
		this.log(LOG_LEVELS.ERROR, message, data)
	}
	
	consoleOutput(logEntry) {
		const { level, message, data } = logEntry
		const style = this.getConsoleStyle(level)
		
		if (data) {
			console.log(`%c[${level}] ${message}`, style, data)
		} else {
			console.log(`%c[${level}] ${message}`, style)
		}
	}
	
	getConsoleStyle(level) {
		const styles = {
			DEBUG: 'color: #666; font-size: 12px;',
			INFO: 'color: #1890ff; font-weight: bold;',
			WARN: 'color: #faad14; font-weight: bold;',
			ERROR: 'color: #f5222d; font-weight: bold; background: #fff2f0;'
		}
		return styles[level] || ''
	}
	
	persistLog(logEntry) {
		try {
			const storedLogs = uni.getStorageSync('app_logs') || []
			storedLogs.push(logEntry)
			
			// 只保留最近500条日志
			if (storedLogs.length > 500) {
				storedLogs.splice(0, storedLogs.length - 500)
			}
			
			uni.setStorageSync('app_logs', storedLogs)
		} catch (error) {
			console.error('Failed to persist log:', error)
		}
	}
	
	getLogs(level = null, limit = 100) {
		let filteredLogs = this.logs
		
		if (level !== null) {
			filteredLogs = this.logs.filter(log => log.level === level)
		}
		
		return filteredLogs.slice(-limit)
	}
	
	clearLogs() {
		this.logs = []
		uni.removeStorageSync('app_logs')
	}
	
	exportLogs() {
		const logs = uni.getStorageSync('app_logs') || []
		return JSON.stringify(logs, null, 2)
	}
}

export const logger = new Logger()

// 错误处理器
class ErrorHandler {
	constructor() {
		this.errorCallbacks = []
		this.init()
	}
	
	init() {
		// 全局错误监听
		uni.onError((error) => {
			this.handleError(error, 'Global Error')
		})
		
		// Promise 错误监听
		uni.onUnhandledRejection((event) => {
			this.handleError(event.reason, 'Unhandled Promise Rejection')
		})
	}
	
	handleError(error, context = 'Unknown') {
		const errorInfo = {
			message: error.message || error,
			stack: error.stack,
			context,
			timestamp: new Date().toISOString(),
			userAgent: navigator.userAgent,
			url: window.location?.href || 'miniprogram'
		}
		
		// 记录错误日志
		logger.error(`${context}: ${errorInfo.message}`, errorInfo)
		
		// 执行错误回调
		this.errorCallbacks.forEach(callback => {
			try {
				callback(errorInfo)
			} catch (callbackError) {
				logger.error('Error in error callback', callbackError)
			}
		})
		
		// 用户友好的错误提示
		this.showUserFriendlyError(errorInfo)
	}
	
	showUserFriendlyError(errorInfo) {
		const friendlyMessages = {
			'Network Error': '网络连接异常，请检查网络设置',
			'Timeout': '请求超时，请稍后重试',
			'Permission denied': '权限不足，请联系管理员',
			'Storage quota exceeded': '存储空间不足，请清理数据'
		}
		
		let message = '系统异常，请稍后重试'
		
		// 匹配友好错误信息
		for (const [key, value] of Object.entries(friendlyMessages)) {
			if (errorInfo.message.includes(key)) {
				message = value
				break
			}
		}
		
		uni.showToast({
			title: message,
			icon: 'none',
			duration: 3000
		})
	}
	
	onError(callback) {
		this.errorCallbacks.push(callback)
	}
	
	// 手动报告错误
	reportError(error, context = 'Manual Report') {
		this.handleError(error, context)
	}
}

export const errorHandler = new ErrorHandler()

// 异步操作包装器
export function asyncWrapper(asyncFn, context = 'Async Operation') {
	return async (...args) => {
		try {
			logger.debug(`Starting ${context}`, { args })
			const result = await asyncFn(...args)
			logger.debug(`Completed ${context}`, { result })
			return result
		} catch (error) {
			errorHandler.reportError(error, context)
			throw error
		}
	}
}

// 重试机制
export async function retryAsync(asyncFn, maxRetries = 3, delay = 1000) {
	let lastError
	
	for (let i = 0; i < maxRetries; i++) {
		try {
			return await asyncFn()
		} catch (error) {
			lastError = error
			logger.warn(`Retry attempt ${i + 1}/${maxRetries} failed`, error)
			
			if (i < maxRetries - 1) {
				await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
			}
		}
	}
	
	throw lastError
}

// 网络请求错误处理
export function handleNetworkError(error) {
	const networkErrors = {
		'ERR_NETWORK': '网络连接失败',
		'ERR_TIMEOUT': '请求超时',
		'ERR_CANCELED': '请求被取消',
		'ERR_BAD_REQUEST': '请求参数错误',
		'ERR_UNAUTHORIZED': '未授权访问',
		'ERR_FORBIDDEN': '访问被禁止',
		'ERR_NOT_FOUND': '资源不存在',
		'ERR_INTERNAL_SERVER': '服务器内部错误'
	}
	
	const errorCode = error.code || 'UNKNOWN'
	const message = networkErrors[errorCode] || '网络请求失败'
	
	logger.error('Network Error', { code: errorCode, message, error })
	
	return {
		code: errorCode,
		message,
		originalError: error
	}
}

// 表单验证错误处理
export function handleValidationError(errors) {
	const errorMessages = []
	
	Object.entries(errors).forEach(([field, error]) => {
		if (Array.isArray(error)) {
			errorMessages.push(...error)
		} else {
			errorMessages.push(error)
		}
	})
	
	const message = errorMessages.join('；')
	
	uni.showToast({
		title: message,
		icon: 'none',
		duration: 3000
	})
	
	logger.warn('Validation Error', { errors, message })
}

// 业务逻辑错误处理
export function handleBusinessError(error) {
	const businessErrors = {
		'EXAM_NOT_FOUND': '考试不存在',
		'EXAM_EXPIRED': '考试已过期',
		'EXAM_COMPLETED': '考试已完成',
		'INSUFFICIENT_PERMISSION': '权限不足',
		'DATA_NOT_FOUND': '数据不存在',
		'OPERATION_FAILED': '操作失败'
	}
	
	const message = businessErrors[error.code] || error.message || '操作失败'
	
	uni.showModal({
		title: '提示',
		content: message,
		showCancel: false
	})
	
	logger.warn('Business Error', error)
}

// 错误边界组件（用于Vue组件）
export const ErrorBoundary = {
	name: 'ErrorBoundary',
	data() {
		return {
			hasError: false,
			error: null
		}
	},
	errorCaptured(error, instance, info) {
		this.hasError = true
		this.error = error
		
		errorHandler.reportError(error, `Component Error: ${info}`)
		
		// 阻止错误继续传播
		return false
	},
	render() {
		if (this.hasError) {
			return this.$slots.fallback || h('div', '出现错误，请刷新页面重试')
		}
		return this.$slots.default
	}
}
