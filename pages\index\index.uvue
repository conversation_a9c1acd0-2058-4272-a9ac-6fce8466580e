/**
 * @description 首页
 * <AUTHOR>
 * @date 2025-04-16
 */

<template>
	<view class="container">
		<!-- 标题 -->
		<view class="title">
			<text>安全生产考试</text>
		</view>
		
		<!-- 输入表单 -->
		<view class="form">
			<view class="input-group">
				<text class="label">姓名</text>
				<input 
					class="input" 
					type="text" 
					v-model="userInfo.name" 
					placeholder="请输入姓名"
				/>
			</view>
			
			<view class="input-group">
				<text class="label">部门</text>
				<input 
					class="input" 
					type="text" 
					v-model="userInfo.department" 
					placeholder="请输入部门"
				/>
			</view>
		</view>
		
		<!-- 开始考试按钮 -->
		<view class="start-btn" :class="{ 'loading': isLoading }" @click="startExam">
			<text v-if="!isLoading">开始考试</text>
			<text v-else>准备中...</text>
		</view>
		
		<!-- 查看题库按钮 -->
		<view class="view-bank-btn" @click="viewQuestionBank">
			<text>查看题库</text>
		</view>
		
		<!-- 管理员入口 -->
		<view class="admin-entry" @click="navigateToAdmin">
			<text>管理员入口</text>
		</view>
	</view>
</template>

<script>
import { getRandomQuestions } from '@/utils/questionBank.js'
import { generateExamByDifficulty } from '@/utils/examConfig.js'
import { loadingManager, messageManager, FormValidator, validationRules } from '@/utils/userExperience.js'
import { logger, errorHandler } from '@/utils/miniprogramErrorHandler.js'
import { dataBackupManager } from '@/utils/dataBackup.js'

export default {
	data() {
		return {
			userInfo: {
				name: '',
				department: ''
			},
			isLoading: false,
			validator: null
		}
	},
	onLoad() {
		// 初始化表单验证器
		this.initValidator()

		// 尝试加载已保存的用户信息
		try {
			const savedUserInfo = uni.getStorageSync('userInfo')
			if (savedUserInfo) {
				this.userInfo = savedUserInfo
				logger.info('User info loaded successfully')
			}
		} catch (error) {
			logger.error('Failed to load user info', error)
			messageManager.error('加载用户信息失败')
		}

		// 执行自动备份
		this.performAutoBackup()
	},
	methods: {
		// 初始化表单验证器
		initValidator() {
			this.validator = new FormValidator()
			this.validator.addRule('name', validationRules.required('请输入姓名'))
			this.validator.addRule('name', validationRules.minLength(2, '姓名至少需要2个字符'))
			this.validator.addRule('department', validationRules.required('请输入部门'))
			this.validator.addRule('department', validationRules.minLength(2, '部门名称至少需要2个字符'))
		},

		// 执行自动备份
		async performAutoBackup() {
			try {
				const result = await dataBackupManager.autoBackup()
				if (result.success && result.backupId) {
					logger.info('Auto backup completed', result)
				}
			} catch (error) {
				logger.error('Auto backup failed', error)
			}
		},

		// 开始考试
		async startExam() {
			// 表单验证
			const validation = this.validator.validate(this.userInfo)
			if (!validation.isValid) {
				const firstError = this.validator.getFirstError()
				messageManager.error(firstError)
				return
			}

			// 显示加载状态
			this.isLoading = true
			loadingManager.show('startExam', { title: '正在准备考试...' })

			try {
				// 保存用户信息
				uni.setStorageSync('userInfo', this.userInfo)
				logger.info('User info saved', this.userInfo)

				// 使用新的题目生成方式，按难度分布
				const questions = generateExamByDifficulty(
					(await import('@/utils/questionBank.js')).questionBank,
					10
				)
				
				// 创建考试对象
				const exam = {
					id: Date.now(),
					title: '安全生产考试',
					description: '安全生产知识测试',
					duration: 15, // 15分钟
					questions: questions,
					startTime: Date.now()
				}
				
				// 保存考试信息
				uni.setStorageSync('currentExam', exam)
				logger.info('Exam created successfully', { examId: exam.id, questionCount: questions.length })

				// 延迟一下让用户看到加载效果
				await new Promise(resolve => setTimeout(resolve, 500))

				// 跳转到答题页面
				uni.navigateTo({
					url: '/pages/examPaper/examPaper'
				})

				messageManager.success('考试准备完成')
			} catch (error) {
				logger.error('Failed to start exam', error)
				errorHandler.reportError(error, 'Start Exam')
				messageManager.error('考试准备失败，请重试')
			} finally {
				this.isLoading = false
				loadingManager.hide('startExam')
			}
		},
		
		navigateToAdmin() {
			uni.navigateTo({
				url: '/pages/admin/login'
			})
		},
		
		// 查看题库
		viewQuestionBank() {
			uni.navigateTo({
				url: '/pages/questionBank'
			})
		}
	}
}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	/* 标题样式 */
	.title {
		margin: 60rpx 0;
	}

	.title text {
		font-size: 48rpx;
		font-weight: bold;
		color: #333;
	}

	/* 表单样式 */
	.form {
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 40rpx;
	}

	.input-group {
		margin-bottom: 30rpx;
	}

	.input-group:last-child {
		margin-bottom: 0;
	}

	.label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
	}

	.input {
		width: 100%;
		height: 80rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
	}

	/* 开始考试按钮样式 */
	.start-btn {
		width: 100%;
		height: 88rpx;
		background-color: #1E90FF;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.start-btn text {
		font-size: 32rpx;
		color: #fff;
	}
	
	/* 查看题库按钮样式 */
	.view-bank-btn {
		width: 100%;
		height: 88rpx;
		background-color: #34C759;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}
	
	.view-bank-btn text {
		font-size: 32rpx;
		color: #fff;
	}
	
	/* 管理员入口样式 */
	.admin-entry {
		width: 100%;
		height: 88rpx;
		background-color: #fff;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1rpx solid #1E90FF;
	}

	.admin-entry text {
		font-size: 32rpx;
		color: #1E90FF;
	}
</style>
