/**
 * @description 首页
 * <AUTHOR>
 * @date 2025-04-16
 */

<template>
	<view class="container">
		<!-- 标题 -->
		<view class="title">
			<text>安全生产考试</text>
		</view>
		
		<!-- 输入表单 -->
		<view class="form">
			<view class="input-group">
				<text class="label">姓名</text>
				<input 
					class="input" 
					type="text" 
					v-model="userInfo.name" 
					placeholder="请输入姓名"
				/>
			</view>
			
			<view class="input-group">
				<text class="label">部门</text>
				<input 
					class="input" 
					type="text" 
					v-model="userInfo.department" 
					placeholder="请输入部门"
				/>
			</view>
		</view>
		
		<!-- 开始考试按钮 -->
		<view class="start-btn" :class="{ 'loading': isLoading }" @click="startExam">
			<text v-if="!isLoading">开始考试</text>
			<text v-else>准备中...</text>
		</view>
		
		<!-- 查看题库按钮 -->
		<view class="view-bank-btn" @click="viewQuestionBank">
			<text>查看题库</text>
		</view>
		
		<!-- 管理员入口 -->
		<view class="admin-entry" @click="navigateToAdmin">
			<text>管理员入口</text>
		</view>
	</view>
</template>

<script>
import { getRandomQuestions } from '@/utils/questionBank.js'

export default {
	data() {
		return {
			userInfo: {
				name: '',
				department: ''
			}
		}
	},
	onLoad() {
		// 尝试加载已保存的用户信息
		const savedUserInfo = uni.getStorageSync('userInfo')
		if (savedUserInfo) {
			this.userInfo = savedUserInfo
		}
	},
	methods: {
		// 开始考试
		async startExam() {
			// 检查用户信息是否完整
			if (!this.userInfo.name || !this.userInfo.department) {
				uni.showToast({
					title: '请先完善个人信息',
					icon: 'none'
				})
				return
			}
			
			// 保存用户信息
			uni.setStorageSync('userInfo', this.userInfo)
			
			try {
				// 随机抽取10道题目
				const questions = getRandomQuestions(10)
				
				// 创建考试对象
				const exam = {
					id: Date.now(),
					title: '安全生产考试',
					description: '安全生产知识测试',
					duration: 15, // 15分钟
					questions: questions,
					startTime: Date.now()
				}
				
				// 保存考试信息
				uni.setStorageSync('currentExam', exam)
				
				// 跳转到答题页面
				uni.navigateTo({
					url: '/pages/examPaper/examPaper'
				})
			} catch (error) {
				console.error('获取题目失败', error)
				uni.showToast({
					title: '获取题目失败',
					icon: 'none'
				})
			}
		},
		
		navigateToAdmin() {
			uni.navigateTo({
				url: '/pages/admin/login'
			})
		},
		
		// 查看题库
		viewQuestionBank() {
			uni.navigateTo({
				url: '/pages/questionBank'
			})
		}
	}
}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	/* 标题样式 */
	.title {
		margin: 60rpx 0;
	}

	.title text {
		font-size: 48rpx;
		font-weight: bold;
		color: #333;
	}

	/* 表单样式 */
	.form {
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 40rpx;
	}

	.input-group {
		margin-bottom: 30rpx;
	}

	.input-group:last-child {
		margin-bottom: 0;
	}

	.label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
	}

	.input {
		width: 100%;
		height: 80rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
	}

	/* 开始考试按钮样式 */
	.start-btn {
		width: 100%;
		height: 88rpx;
		background-color: #1E90FF;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.start-btn text {
		font-size: 32rpx;
		color: #fff;
	}
	
	/* 查看题库按钮样式 */
	.view-bank-btn {
		width: 100%;
		height: 88rpx;
		background-color: #34C759;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}
	
	.view-bank-btn text {
		font-size: 32rpx;
		color: #fff;
	}
	
	/* 管理员入口样式 */
	.admin-entry {
		width: 100%;
		height: 88rpx;
		background-color: #fff;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1rpx solid #1E90FF;
	}

	.admin-entry text {
		font-size: 32rpx;
		color: #1E90FF;
	}
</style>
