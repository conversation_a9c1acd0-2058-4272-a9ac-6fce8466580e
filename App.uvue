/**
 * @description 应用入口文件
 * <AUTHOR>
 * @date 2025-04-16
 */

<script lang="uts">
	import { autoInit } from '@/utils/systemInit.js'

	let firstBackTime = 0
	export default {
		onLaunch: async function () {
			console.log('App Launch')

			// 初始化系统
			try {
				const initResult = await autoInit()
				if (initResult.success) {
					console.log('System initialized successfully')
				} else {
					console.error('System initialization failed:', initResult.error)
				}
			} catch (error) {
				console.error('System initialization error:', error)
			}
		},
		onShow: function () {
			console.log('App Show')
		},
		onHide: function () {
			console.log('App Hide')
		},
		// #ifdef APP-ANDROID
		onLastPageBackPress: function () {
			console.log('App LastPageBackPress')
			if (firstBackTime == 0) {
				uni.showToast({
					title: '再按一次退出应用',
					position: 'bottom',
				})
				firstBackTime = Date.now()
				setTimeout(() => {
					firstBackTime = 0
				}, 2000)
			} else if (Date.now() - firstBackTime < 2000) {
				firstBackTime = Date.now()
				uni.exit()
			}
		},
		// #endif
		onExit: function () {
			console.log('App Exit')
		},
	}
</script>

<style>
	/* 全局样式 */
	page {
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}

	/*每个页面公共css */
	.uni-row {
		flex-direction: row;
	}

	.uni-column {
		flex-direction: column;
	}
</style>